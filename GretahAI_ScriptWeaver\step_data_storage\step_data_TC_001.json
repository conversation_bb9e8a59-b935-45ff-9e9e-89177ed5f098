{"test_case_id": "TC_001", "timestamp": "2025-05-30T22:52:47.226316", "data_hash": "ed8cb4f4b144fcabab2a3ece3fa6988d4ff1276cddaff17c152ce215fd1f251e", "step_count": 4, "metadata": {"source": "real_time_update", "operation": "script_generated_step_4", "update_timestamp": "2025-05-30T22:51:50.705043", "step_count": 4, "step_no": "4", "script_file_path": "generated_tests\\test_TC_001_4_1748670710_merged.py", "script_content_length": 4841, "step_specific_file": "generated_tests\\test_TC_001_4_1748670710_step_only.py", "generation_method": "ai_generated", "test_case_objective": "Verify a successful login with valid user credentials.", "hybrid_editing_enabled": false, "has_manual_steps": false, "save_timestamp": "2025-05-30T22:51:50.705043", "url_tracking_update": {"step_no": "4", "timestamp": "2025-05-30T22:52:47.226316", "operation": "url_tracking_update"}}, "step_data": [{"step_no": "1", "step_type": "ui", "action": "navigate", "locator_strategy": "", "locator": "https://the-internet.herokuapp.com/login", "test_data_param": "{{login_page_url}}", "expected_result": "login_page", "assertion_type": "url_equals", "condition": "", "timeout": 10, "step_description": "Navigate to the login page", "current_url": "https://the-internet.herokuapp.com/login", "url_history": [{"url": "https://the-internet.herokuapp.com/login", "timestamp": "20250530_223850", "step_no": "1", "action": "test_execution_complete"}], "step_execution_urls": {"start_url": null, "end_url": "https://the-internet.herokuapp.com/login", "intermediate_urls": [], "capture_method": "pytest_hook"}, "url_capture_timestamp": "2025-05-30T22:38:58.118082", "_script_generated": true, "_script_file_path": "generated_tests\\test_TC_001_1_1748669898_merged.py", "_script_content_length": 1146, "_script_generation_timestamp": "2025-05-30T22:38:18.708362", "_script_validation_pending": true, "_step_specific_script_path": "generated_tests\\test_TC_001_1_1748669898_step_only.py"}, {"step_no": "2", "step_type": "ui", "action": "type", "locator_strategy": "id", "locator": "userid", "test_data_param": "{{username}}", "expected_result": "userid_entered", "assertion_type": "text_equals", "condition": "", "timeout": 10, "step_description": "Enter valid User ID", "current_url": "https://the-internet.herokuapp.com/login", "url_history": [{"url": "https://the-internet.herokuapp.com/login", "timestamp": "20250530_224015", "step_no": "2", "action": "test_execution_complete"}], "url_capture_timestamp": "2025-05-30T22:40:25.989728", "step_execution_urls": {"start_url": null, "end_url": "https://the-internet.herokuapp.com/login", "intermediate_urls": [], "capture_method": "pytest_hook"}, "_test_data": {"manual_input_for_step_2": "tomsmith"}, "_test_data_generated": true, "_test_data_timestamp": "2025-05-30T22:39:35.574566", "_test_data_method": "manual_entry", "_script_generated": true, "_script_file_path": "generated_tests\\test_TC_001_2_1748669984_merged.py", "_script_content_length": 1881, "_script_generation_timestamp": "2025-05-30T22:39:44.457615", "_script_validation_pending": true, "_step_specific_script_path": "generated_tests\\test_TC_001_2_1748669984_step_only.py"}, {"step_no": "3", "step_type": "ui", "action": "type", "locator_strategy": "id", "locator": "password", "test_data_param": "{{password}}", "expected_result": "password_entered", "assertion_type": "text_equals", "condition": "", "timeout": 10, "step_description": "Enter valid password", "current_url": "https://the-internet.herokuapp.com/login", "url_history": [{"url": "https://the-internet.herokuapp.com/login", "timestamp": "20250530_224139", "step_no": "3", "action": "test_execution_complete"}], "url_capture_timestamp": "2025-05-30T22:41:49.773907", "step_execution_urls": {"start_url": null, "end_url": "https://the-internet.herokuapp.com/login", "intermediate_urls": [], "capture_method": "pytest_hook"}, "_test_data": {"manual_input_for_step_3": "SuperSecretPassword!"}, "_test_data_generated": true, "_test_data_timestamp": "2025-05-30T22:40:55.441752", "_test_data_method": "manual_entry", "_script_generated": true, "_script_file_path": "generated_tests\\test_TC_001_3_1748670064_merged.py", "_script_content_length": 2635, "_script_generation_timestamp": "2025-05-30T22:41:04.144997", "_script_validation_pending": true, "_step_specific_script_path": "generated_tests\\test_TC_001_3_1748670064_step_only.py"}, {"step_no": "4", "step_type": "ui", "action": "click", "locator_strategy": "id", "locator": "login_button", "test_data_param": "", "expected_result": "dashboard_page", "assertion_type": "url_contains", "condition": "", "timeout": 10, "step_description": "Click the login button", "current_url": "https://the-internet.herokuapp.com/login", "url_history": [{"url": "https://the-internet.herokuapp.com/login", "timestamp": "20250530_225235", "step_no": "4", "action": "test_execution_complete"}], "url_capture_timestamp": "2025-05-30T22:52:47.226316", "step_execution_urls": {"start_url": null, "end_url": "https://the-internet.herokuapp.com/login", "intermediate_urls": [], "capture_method": "pytest_hook"}, "_script_generated": true, "_script_file_path": "generated_tests\\test_TC_001_4_1748670710_merged.py", "_script_content_length": 4841, "_script_generation_timestamp": "2025-05-30T22:51:50.705043", "_script_validation_pending": true, "_step_specific_script_path": "generated_tests\\test_TC_001_4_1748670710_step_only.py"}]}