{"test_case_id": "TC_001", "timestamp": "2025-05-30T19:04:52.541178", "data_hash": "abdf7ab2b3300b024abd3eec9caf5b730fa682ff52625419c25c43353828bf16", "step_count": 4, "metadata": {"source": "real_time_update", "operation": "script_generated_step_4", "update_timestamp": "2025-05-30T19:04:24.277301", "step_count": 4, "step_no": "4", "script_file_path": "generated_tests\\test_TC_001_4_1748657064_merged.py", "script_content_length": 3243, "step_specific_file": "generated_tests\\test_TC_001_4_1748657064_step_only.py", "generation_method": "ai_generated", "test_case_objective": "Verify that a user with valid credentials can successfully log in.", "hybrid_editing_enabled": false, "has_manual_steps": false, "save_timestamp": "2025-05-30T19:04:24.277301", "url_tracking_update": {"step_no": "4", "timestamp": "2025-05-30T19:04:52.541178", "operation": "url_tracking_update"}}, "step_data": [{"step_no": "1", "step_type": "ui", "action": "navigate", "locator_strategy": "", "locator": "https://the-internet.herokuapp.com/login", "test_data_param": "{{login_page_url}}", "expected_result": "login_page", "assertion_type": "url_equals", "condition": "", "timeout": 10, "step_description": "Navigate to the login page", "current_url": "https://the-internet.herokuapp.com/login", "url_history": [{"url": "https://the-internet.herokuapp.com/login", "timestamp": "20250530_180319", "step_no": "1", "action": "test_execution_complete"}], "step_execution_urls": {"start_url": null, "end_url": "https://the-internet.herokuapp.com/login", "intermediate_urls": [], "capture_method": "pytest_hook"}, "url_capture_timestamp": "2025-05-30T18:03:27.299873", "_script_generated": true, "_script_file_path": "generated_tests\\test_TC_001_1_1748653377_merged.py", "_script_content_length": 1268, "_script_generation_timestamp": "2025-05-30T18:02:57.860405", "_script_validation_pending": true, "_step_specific_script_path": "generated_tests\\test_TC_001_1_1748653377_step_only.py"}, {"step_no": "2", "step_type": "ui", "action": "type", "locator_strategy": "id", "locator": "username", "test_data_param": "{{username}}", "expected_result": "username_entered", "assertion_type": "text_equals", "condition": "", "timeout": 10, "step_description": "Enter valid username", "current_url": "https://the-internet.herokuapp.com/login", "url_history": [{"url": "https://the-internet.herokuapp.com/login", "timestamp": "20250530_180609", "step_no": "2", "action": "test_execution_complete"}], "url_capture_timestamp": "2025-05-30T18:06:18.377715", "step_execution_urls": {"start_url": null, "end_url": "https://the-internet.herokuapp.com/login", "intermediate_urls": [], "capture_method": "pytest_hook"}, "_test_data": {"manual_input_for_step_2": "tomsmith"}, "_test_data_generated": true, "_test_data_timestamp": "2025-05-30T18:05:39.992141", "_test_data_method": "manual_entry", "_script_generated": true, "_script_file_path": "generated_tests\\test_TC_001_2_1748653549_merged.py", "_script_content_length": 1974, "_script_generation_timestamp": "2025-05-30T18:05:49.153339", "_script_validation_pending": true, "_step_specific_script_path": "generated_tests\\test_TC_001_2_1748653549_step_only.py"}, {"step_no": "3", "step_type": "ui", "action": "type", "locator_strategy": "id", "locator": "password", "test_data_param": "{{password}}", "expected_result": "password_entered", "assertion_type": "text_equals", "condition": "", "timeout": 10, "step_description": "Enter valid password", "current_url": "https://the-internet.herokuapp.com/login", "url_history": [{"url": "https://the-internet.herokuapp.com/login", "timestamp": "20250530_180855", "step_no": "3", "action": "test_execution_complete"}], "url_capture_timestamp": "2025-05-30T18:09:06.066572", "step_execution_urls": {"start_url": null, "end_url": "https://the-internet.herokuapp.com/login", "intermediate_urls": [], "capture_method": "pytest_hook"}, "_test_data": {"manual_input_for_step_3": "SuperSecretPassword!"}, "_test_data_generated": true, "_test_data_timestamp": "2025-05-30T18:08:26.943720", "_test_data_method": "manual_entry", "_script_generated": true, "_script_file_path": "generated_tests\\test_TC_001_3_1748653717_merged.py", "_script_content_length": 2727, "_script_generation_timestamp": "2025-05-30T18:08:38.017557", "_script_validation_pending": true, "_step_specific_script_path": "generated_tests\\test_TC_001_3_1748653717_step_only.py"}, {"step_no": "4", "step_type": "ui", "action": "click", "locator_strategy": "css", "locator": "#login-button", "test_data_param": "", "expected_result": "dashboard_page", "assertion_type": "element_visible", "condition": "", "timeout": 10, "step_description": "Click the login button", "current_url": "https://the-internet.herokuapp.com/login", "url_history": [{"url": "https://the-internet.herokuapp.com/login", "timestamp": "20250530_183257", "step_no": "4", "action": "test_execution_complete"}, {"url": "https://the-internet.herokuapp.com/login", "timestamp": "20250530_184910", "step_no": "4", "action": "test_execution_complete"}, {"url": "https://the-internet.herokuapp.com/login", "timestamp": "20250530_190440", "step_no": "4", "action": "test_execution_complete"}], "url_capture_timestamp": "2025-05-30T19:04:52.541178", "step_execution_urls": {"start_url": null, "end_url": "https://the-internet.herokuapp.com/login", "intermediate_urls": [], "capture_method": "pytest_hook"}, "_script_generated": true, "_script_file_path": "generated_tests\\test_TC_001_4_1748657064_merged.py", "_script_content_length": 3243, "_script_generation_timestamp": "2025-05-30T19:04:24.277301", "_script_validation_pending": true, "_step_specific_script_path": "generated_tests\\test_TC_001_4_1748657064_step_only.py"}]}